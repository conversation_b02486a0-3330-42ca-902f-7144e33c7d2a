/**
 *
 * Author  : <PERSON><PERSON><PERSON> (adi1090x)
 * Github  : @adi1090x
 * 
 * Rofi Theme File
 * Rofi Version : 1.7.3
 **/

/*****----- Configuration -----*****/
configuration {
	modi : "drun,run,filebrowser,window";
    show-icons : true;
    display-drun : "";
    display-run : "";
    display-filebrowser : "";
    display-window : "";
	drun-display-format : "{name}";
	window-format : "{w} · {c} · {t}";
}

/*****----- Global Properties -----*****/
* {
    transparency: "real";
}

/*****----- Main Window -----*****/
window {
    transparency:                "real";
    background-color:            rgba(30, 30, 46, 0.85);
    border-radius:               12px;
}

/*****----- Main Box -----*****/
mainbox {
    background-color:            rgba(30, 30, 46, 0.90);
}

/*****----- Inputbar -----*****/
inputbar {
    background-color:            rgba(49, 50, 68, 0.80);
}

prompt {
    enabled : true;
    background-color : inherit;
    text-color : inherit;
}
textbox-prompt-colon {
    enabled : true;
    padding : 5px 0px;
    expand : false;
    str : "";
    background-color : inherit;
    text-color : inherit;
}
entry {
    enabled : true;
    padding : 5px 0px;
    background-color : inherit;
    text-color : inherit;
    cursor : text;
    placeholder : " Search...";
    placeholder-color : inherit;
}
num-filtered-rows {
    enabled : true;
    expand : false;
    background-color : inherit;
    text-color : inherit;
}
textbox-num-sep {
    enabled : true;
    expand : false;
    str : "/";
    background-color : inherit;
    text-color : inherit;
}
num-rows {
    enabled : true;
    expand : false;
    background-color : inherit;
    text-color : inherit;
}
case-indicator {
    enabled : true;
    background-color : inherit;
    text-color : inherit;
}

/*****----- Listview -----*****/
listview {
    enabled : true;
    columns : 1;
    lines : 8;
    cycle : true;
    dynamic : true;
    scrollbar : true;
    layout : vertical;
    reverse : false;
    fixed-height : true;
    fixed-columns : true;
    spacing : 5px;
    margin : 0px;
    padding : 0px;
    border : 0px solid;
    border-radius : 0px;
    border-color : @border-colour;
    background-color : transparent;
    text-color : @foreground-colour;
    cursor : "default";
}
scrollbar {
    handle-width : 5px ;
    handle-color : @handle-colour;
    border-radius : 10px;
    // background-color : @alternate-background;
}

/*****----- Elements -----*****/
element {
    enabled : true;
    spacing : 10px;
    margin : 0px;
    padding : 5px 10px;
    border : 0px solid;
    border-radius : 10px;
    border-color : @border-colour;
    background-color : transparent;
    text-color : @foreground-colour;
    cursor : default;
}
element normal.normal {
    background-color : var(normal-background);
    text-color : var(normal-foreground);
}
element normal.urgent {
    background-color : var(urgent-background);
    text-color : var(urgent-foreground);
}
element normal.active {
    // background-color : var(active-background);
    text-color : var(active-foreground);
}
element selected.normal {
    background-color : var(selected-normal-background);
    text-color : var(selected-normal-foreground);
}
element selected.urgent {
    // background-color : var(selected-urgent-background);
    text-color : var(selected-urgent-foreground);
}
element selected.active {
    background-color : var(selected-active-background);
    text-color : var(selected-active-foreground);
}
element alternate.normal {
    background-color : var(alternate-normal-background);
    text-color : var(alternate-normal-foreground);
}
element alternate.urgent {
    background-color : var(alternate-urgent-background);
    text-color : var(alternate-urgent-foreground);
}
element alternate.active {
    // background-color : var(alternate-active-background);
    text-color : var(alternate-active-foreground);
}
element-icon {
    background-color : transparent;
    text-color : inherit;
    size : 24px;
    cursor : inherit;
}
element-text {
    background-color : transparent;
    text-color : inherit;
    highlight : inherit;
    cursor : inherit;
    vertical-align : 0.5;
    horizontal-align : 0.0;
}

/*****----- Mode Switcher -----*****/
mode-switcher{
    enabled : true;
    spacing : 10px;
    margin : 0px;
    padding : 0px;
    border : 0px solid;
    border-radius : 0px;
    border-color : @border-colour;
    background-color : transparent;
    text-color : @selected-normal-foreground;
}
button {
    padding : 5px 10px;
    border : 0px solid;
    border-radius : 10px;
    border-color : @border-colour;
    // background-color : @alternate-background;
    text-color : inherit;
    cursor : default;
}
button selected {
    background-color : var(selected-normal-background);
    text-color : var(selected-normal-foreground);
}

/*****----- Message -----*****/
message {
    enabled : true;
    margin : 0px;
    padding : 0px;
    border : 0px solid;
    border-radius : 0px 0px 0px 0px;
    border-color : @border-colour;
    background-color : transparent;
    text-color : @foreground-colour;
}
textbox {
    padding : 8px 10px;
    border : 0px solid;
    border-radius : 10px;
    border-color : @border-colour;
    // background-color : @alternate-background;
    text-color : @foreground-colour;
    vertical-align : 0.5;
    horizontal-align : 0.0;
    highlight : none;
    placeholder-color : @foreground-colour;
    blink : true;
    markup : true;
}
error-message {
    padding : 10px;
    border : 2px solid;
    border-radius : 10px;
    border-color : @border-colour;
    background-color : @background-colour;
    text-color : @foreground-colour;
}
