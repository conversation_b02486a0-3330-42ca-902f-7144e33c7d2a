* {
    /* Base colors */
    background:     rgba(30, 33, 39, 0.8);
    background-alt: rgba(40, 43, 49, 0.8);
    foreground:     #FFFFFFFF;
    selected:       rgba(97, 175, 239, 0.8);
    active:         rgba(152, 195, 121, 0.8);
    urgent:         rgba(224, 108, 117, 0.8);

    /* Extended variables for theme compatibility */
    border-colour:               var(selected);
    handle-colour:               var(selected);
    background-colour:           var(background);
    foreground-colour:           var(foreground);
    alternate-background:        var(background-alt);
    normal-background:           var(background);
    normal-foreground:           var(foreground);
    urgent-background:           var(urgent);
    urgent-foreground:           var(background);
    active-background:           var(active);
    active-foreground:           var(background);
    selected-normal-background:  var(selected);
    selected-normal-foreground:  var(background);
    selected-urgent-background:  var(active);
    selected-urgent-foreground:  var(background);
    selected-active-background:  var(urgent);
    selected-active-foreground:  var(background);
    alternate-normal-background: var(background);
    alternate-normal-foreground: var(foreground);
    alternate-urgent-background: var(urgent);
    alternate-urgent-foreground: var(background);
    alternate-active-background: var(active);
    alternate-active-foreground: var(background);
}
