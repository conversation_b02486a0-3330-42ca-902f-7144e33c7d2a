[2025-07-24 10:06:58] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:06:59] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:06:59] === AleatoryWall Script Started ===
[2025-07-24 10:06:59] [ERROR] Missing dependencies: wal
[2025-07-24 10:06:59] [ERROR] Please install the missing packages
[2025-07-24 10:10:25] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:10:26] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:10:26] === AleatoryWall Script Started ===
[2025-07-24 10:10:26] [ERROR] Missing dependencies: wal
[2025-07-24 10:10:26] [ERROR] Please install the missing packages
[2025-07-24 10:10:57] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:10:58] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:10:58] === AleatoryWall Script Started ===
[2025-07-24 10:10:58] [ERROR] Missing dependencies: wal
[2025-07-24 10:10:58] [ERROR] Please install the missing packages
[2025-07-24 10:52:15] [DEBUG] Searching for wallpaper directories...
[2025-07-24 10:52:15] [DEBUG] Checking directory: /home/<USER>/Wallpapers
[2025-07-24 10:52:15] [DEBUG] Directory exists: /home/<USER>/Wallpapers
[2025-07-24 10:52:15] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:52:15] [DEBUG] Using wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 10:52:15] [DEBUG] Checking for previous instances of AleatoryWall.sh
[2025-07-24 10:52:15] [DEBUG] Killing previous instance with PID: 8340
[2025-07-24 10:52:16] [DEBUG] Searching for image files in /home/<USER>/Wallpapers
[2025-07-24 10:52:16] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:52:16] === AleatoryWall Script Started ===
[2025-07-24 10:52:16] [ERROR] Missing dependencies: wal
[2025-07-24 10:52:16] [ERROR] Please install the missing packages
[2025-07-24 10:53:08] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:53:09] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:53:09] === AleatoryWall Script Started ===
[2025-07-24 10:53:09] Running in single-change mode
[2025-07-24 10:53:09] Changing wallpaper to: WALL12.jpg
[2025-07-24 10:53:48] Script terminating, cleaning up...
[2025-07-24 10:54:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:54:05] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:54:05] === AleatoryWall Script Started ===
[2025-07-24 10:54:05] Running in single-change mode
[2025-07-24 10:54:05] Changing wallpaper to: WALL10.png
[2025-07-24 10:55:07] Script terminating, cleaning up...
[2025-07-24 10:55:19] [DEBUG] Searching for wallpaper directories...
[2025-07-24 10:55:19] [DEBUG] Checking directory: /home/<USER>/Wallpapers
[2025-07-24 10:55:19] [DEBUG] Directory exists: /home/<USER>/Wallpapers
[2025-07-24 10:55:19] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 10:55:19] [DEBUG] Using wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 10:55:19] [DEBUG] Checking for previous instances of AleatoryWall.sh
[2025-07-24 10:55:19] [DEBUG] Killing previous instance with PID: 9539
[2025-07-24 10:55:20] [DEBUG] Searching for image files in /home/<USER>/Wallpapers
[2025-07-24 10:55:20] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 10:55:20] === AleatoryWall Script Started ===
[2025-07-24 10:55:20] [DEBUG] Optional dependency not found: wal (features may be limited)
[2025-07-24 10:55:20] [DEBUG] All required dependencies are available
[2025-07-24 10:55:20] Running in single-change mode
[2025-07-24 10:55:20] [DEBUG] Starting wallpaper change process
[2025-07-24 10:55:20] Changing wallpaper to: WALL12.jpg
[2025-07-24 10:55:20] [DEBUG] Checking if swww daemon is running
[2025-07-24 10:55:20] [DEBUG] swww daemon already running
[2025-07-24 10:55:20] [DEBUG] Setting wallpaper with swww
[2025-07-24 10:55:21] [DEBUG] swww wallpaper set successfully
[2025-07-24 10:55:21] [DEBUG] pywal not available, skipping color generation
[2025-07-24 10:55:21] [DEBUG] Restarting SwayNC
[2025-07-24 11:22:06] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:22:07] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 11:22:07] === AleatoryWall Script Started ===
[2025-07-24 11:22:07] AleatoryWall daemon started in background
[2025-07-24 11:22:07] Starting AleatoryWall daemon
[2025-07-24 11:22:07] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 11:22:07] Found 14 wallpapers
[2025-07-24 11:22:07] Change interval: 60 minutes
[2025-07-24 11:22:07] Changing wallpaper to: WALL3(1).jpg
[2025-07-24 11:25:59] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:26:00] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 11:26:00] === AleatoryWall Script Started ===
[2025-07-24 11:26:00] Starting AleatoryWall daemon
[2025-07-24 11:26:00] AleatoryWall daemon started in background
[2025-07-24 11:26:00] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 11:26:00] Found 14 wallpapers
[2025-07-24 11:26:00] Change interval: 60 minutes
[2025-07-24 11:26:00] Changing wallpaper to: WALL8.jpg
[2025-07-24 11:55:55] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:55:56] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:55:58] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 11:55:58] === AleatoryWall Script Started ===
[2025-07-24 11:55:58] Running in single-change mode
[2025-07-24 11:55:58] Changing wallpaper to: WALL12.jpg
[2025-07-24 11:56:03] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:03] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:03] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:03] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:04] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:08] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 11:56:09] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 11:56:09] === AleatoryWall Script Started ===
[2025-07-24 11:56:09] AleatoryWall daemon started in background
[2025-07-24 11:56:09] Starting AleatoryWall daemon
[2025-07-24 11:56:09] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 11:56:09] Found 14 wallpapers
[2025-07-24 11:56:09] Change interval: 60 minutes
[2025-07-24 11:56:09] Changing wallpaper to: WALL2.jpg
[2025-07-24 13:27:37] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 13:27:38] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 13:27:38] === AleatoryWall Script Started ===
[2025-07-24 13:27:38] AleatoryWall daemon started in background
[2025-07-24 13:27:38] Starting AleatoryWall daemon
[2025-07-24 13:27:38] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 13:27:38] Found 14 wallpapers
[2025-07-24 13:27:38] Change interval: 60 minutes
[2025-07-24 13:27:38] Changing wallpaper to: WALL4.jpg
[2025-07-24 13:42:11] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 13:42:12] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 13:42:12] === AleatoryWall Script Started ===
[2025-07-24 13:42:12] AleatoryWall daemon started in background
[2025-07-24 13:42:12] Starting AleatoryWall daemon
[2025-07-24 13:42:12] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 13:42:12] Found 14 wallpapers
[2025-07-24 13:42:12] Change interval: 60 minutes
[2025-07-24 13:42:13] Changing wallpaper to: WALL2.jpg
[2025-07-24 13:42:31] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 13:42:32] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 13:42:32] === AleatoryWall Script Started ===
[2025-07-24 13:42:32] AleatoryWall daemon started in background
[2025-07-24 13:42:32] Starting AleatoryWall daemon
[2025-07-24 13:42:32] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 13:42:32] Found 14 wallpapers
[2025-07-24 13:42:32] Change interval: 60 minutes
[2025-07-24 13:42:32] Changing wallpaper to: WALL1.jpg
[2025-07-24 16:38:56] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 16:38:57] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 16:38:57] === AleatoryWall Script Started ===
[2025-07-24 16:38:57] Starting AleatoryWall daemon
[2025-07-24 16:38:57] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 16:38:57] AleatoryWall daemon started in background
[2025-07-24 16:38:57] Found 14 wallpapers
[2025-07-24 16:38:57] Change interval: 60 minutes
[2025-07-24 16:38:57] Changing wallpaper to: WALL12.jpg
[2025-07-24 19:04:25] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 19:04:26] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 19:04:26] === AleatoryWall Script Started ===
[2025-07-24 19:04:26] Starting AleatoryWall daemon
[2025-07-24 19:04:26] AleatoryWall daemon started in background
[2025-07-24 19:04:26] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 19:04:26] Found 14 wallpapers
[2025-07-24 19:04:26] Change interval: 60 minutes
[2025-07-24 19:04:26] Changing wallpaper to: WALL3(1).jpg
[2025-07-24 22:14:27] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 22:14:28] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 22:14:28] === AleatoryWall Script Started ===
[2025-07-24 22:14:28] AleatoryWall daemon started in background
[2025-07-24 22:14:28] Starting AleatoryWall daemon
[2025-07-24 22:14:28] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 22:14:28] Found 14 wallpapers
[2025-07-24 22:14:28] Change interval: 60 minutes
[2025-07-24 22:14:28] Changing wallpaper to: WALL5.jpeg
[2025-07-24 22:39:56] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 22:39:57] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 22:39:57] === AleatoryWall Script Started ===
[2025-07-24 22:39:57] Starting AleatoryWall daemon
[2025-07-24 22:39:57] AleatoryWall daemon started in background
[2025-07-24 22:39:57] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 22:39:57] Found 14 wallpapers
[2025-07-24 22:39:57] Change interval: 60 minutes
[2025-07-24 22:39:57] Changing wallpaper to: WALL10.png
[2025-07-24 22:55:08] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-24 22:55:09] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-24 22:55:09] === AleatoryWall Script Started ===
[2025-07-24 22:55:09] Starting AleatoryWall daemon
[2025-07-24 22:55:09] AleatoryWall daemon started in background
[2025-07-24 22:55:09] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-24 22:55:09] Found 14 wallpapers
[2025-07-24 22:55:09] Change interval: 60 minutes
[2025-07-24 22:55:09] Changing wallpaper to: WALL7.jpg
[2025-07-25 09:39:18] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-25 09:39:19] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-25 09:39:19] === AleatoryWall Script Started ===
[2025-07-25 09:39:19] AleatoryWall daemon started in background
[2025-07-25 09:39:19] Starting AleatoryWall daemon
[2025-07-25 09:39:19] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-25 09:39:19] Found 14 wallpapers
[2025-07-25 09:39:19] Change interval: 60 minutes
[2025-07-25 09:39:19] Changing wallpaper to: WALL13.jpg
[2025-07-25 13:24:23] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-25 13:24:24] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-25 13:24:24] === AleatoryWall Script Started ===
[2025-07-25 13:24:24] AleatoryWall daemon started in background
[2025-07-25 13:24:24] Starting AleatoryWall daemon
[2025-07-25 13:24:24] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-25 13:24:24] Found 14 wallpapers
[2025-07-25 13:24:24] Change interval: 60 minutes
[2025-07-25 13:24:24] Changing wallpaper to: WALL13.jpg
[2025-07-25 13:42:53] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-25 13:42:54] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-25 13:42:54] === AleatoryWall Script Started ===
[2025-07-25 13:42:54] AleatoryWall daemon started in background
[2025-07-25 13:42:54] Starting AleatoryWall daemon
[2025-07-25 13:42:54] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-25 13:42:54] Found 14 wallpapers
[2025-07-25 13:42:54] Change interval: 60 minutes
[2025-07-25 13:42:54] Changing wallpaper to: WALL2.jpg
[2025-07-25 19:06:38] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-25 19:06:39] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-25 19:06:39] === AleatoryWall Script Started ===
[2025-07-25 19:06:39] AleatoryWall daemon started in background
[2025-07-25 19:06:39] Starting AleatoryWall daemon
[2025-07-25 19:06:39] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-25 19:06:39] Found 14 wallpapers
[2025-07-25 19:06:39] Change interval: 60 minutes
[2025-07-25 19:06:39] Changing wallpaper to: WALL2.jpg
[2025-07-26 11:07:33] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-26 11:07:35] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-26 11:07:35] === AleatoryWall Script Started ===
[2025-07-26 11:07:35] AleatoryWall daemon started in background
[2025-07-26 11:07:35] Starting AleatoryWall daemon
[2025-07-26 11:07:35] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-26 11:07:35] Found 14 wallpapers
[2025-07-26 11:07:35] Change interval: 60 minutes
[2025-07-26 11:07:35] Changing wallpaper to: WALL3(1).jpg
[2025-07-26 22:07:51] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-26 22:07:52] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-26 22:07:52] === AleatoryWall Script Started ===
[2025-07-26 22:07:52] AleatoryWall daemon started in background
[2025-07-26 22:07:52] Starting AleatoryWall daemon
[2025-07-26 22:07:52] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-26 22:07:52] Found 14 wallpapers
[2025-07-26 22:07:52] Change interval: 60 minutes
[2025-07-26 22:07:52] Changing wallpaper to: WALL16.jpg
[2025-07-26 22:22:07] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-26 22:22:09] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-26 22:22:09] === AleatoryWall Script Started ===
[2025-07-26 22:22:09] AleatoryWall daemon started in background
[2025-07-26 22:22:09] Starting AleatoryWall daemon
[2025-07-26 22:22:09] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-26 22:22:09] Found 14 wallpapers
[2025-07-26 22:22:09] Change interval: 60 minutes
[2025-07-26 22:22:09] Changing wallpaper to: WALL3.png
[2025-07-27 12:49:16] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-27 12:49:18] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-27 12:49:18] === AleatoryWall Script Started ===
[2025-07-27 12:49:18] Starting AleatoryWall daemon
[2025-07-27 12:49:18] AleatoryWall daemon started in background
[2025-07-27 12:49:18] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-27 12:49:18] Found 14 wallpapers
[2025-07-27 12:49:18] Change interval: 60 minutes
[2025-07-27 12:49:18] Changing wallpaper to: WALL4.jpg
[2025-07-27 18:43:44] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-27 18:43:46] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-27 18:43:46] === AleatoryWall Script Started ===
[2025-07-27 18:43:46] Starting AleatoryWall daemon
[2025-07-27 18:43:46] AleatoryWall daemon started in background
[2025-07-27 18:43:46] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-27 18:43:46] Found 14 wallpapers
[2025-07-27 18:43:46] Change interval: 60 minutes
[2025-07-27 18:43:46] Changing wallpaper to: WALL13.jpg
[2025-07-27 20:24:53] [SUCCESS] Found wallpaper directory: /home/<USER>/Wallpapers (14 images)
[2025-07-27 20:24:54] [SUCCESS] Found 14 wallpaper(s) in /home/<USER>/Wallpapers
[2025-07-27 20:24:54] === AleatoryWall Script Started ===
[2025-07-27 20:24:54] AleatoryWall daemon started in background
[2025-07-27 20:24:54] Starting AleatoryWall daemon
[2025-07-27 20:24:54] Wallpaper directory: /home/<USER>/Wallpapers
[2025-07-27 20:24:54] Found 14 wallpapers
[2025-07-27 20:24:54] Change interval: 60 minutes
[2025-07-27 20:24:54] Changing wallpaper to: WALL10.png
