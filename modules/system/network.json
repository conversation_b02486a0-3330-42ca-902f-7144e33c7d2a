{"network#2": {"format-wifi": "{icon}", "format-ethernet": "{ipaddr}/{cidr} ", "format-disconnected": "<span color='#b4befe'>󰖪 </span>OFF", "format-icons": ["󰤟", "󰤢", "󰤥", "󰤨"], "max-length": 50, "tooltip-format-wifi": "IP: {ipaddr}\n{essid} ({signalStrength}%)", "on-click": "nm-applet", "interval": 5}, "network": {"format": "{ifname}", "format-wifi": "", "format-ethernet": "{ipaddr}/{cidr} ", "format-disconnected": "<span color='#b4befe'>󰖪 </span>OFFLINE", "max-length": 50, "tooltip-format": " {ifname} via {gwaddri}", "tooltip-format-wifi": "  {ifname} @ {essid}\nIP: {ipaddr}\nStrength: {signalStrength}%\nFreq: {frequency}MHz\nUp: {bandwidthUpBits} Down: {bandwidthDownBits}", "tooltip-format-ethernet": " {ifname}\nIP: {ipaddr}\n up: {bandwidthUpBits} down: {bandwidthDownBits}", "tooltip-format-disconnected": "Disconnected", "signal": {"good": 70, "bad": 40}}, "custom/wifi": {"format": "{}", "exec": "~/.config/waybar/scripts/network_signal.sh", "interval": 10}}