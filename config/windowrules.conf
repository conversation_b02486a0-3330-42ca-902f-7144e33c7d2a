# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                    Windowrules Configuration                ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

# Windows Rules https://wiki.hyprland.org/0.45.0/Configuring/Window-Rules/ #


# Increase the opacity
windowrule = opacity 0.95, class:^(thunar|nemo)$
windowrule = opacity 0.96, class:^(discord|armcord|webcord)$
windowrule = opacity 0.95, title:^(QQ|Telegram)$
windowrule = opacity 0.95, title:^(NetEase Cloud Music Gtk4)$
windowrule = opacity 0.95, class:^(VSCodium)$

# General window rules
windowrule = float, title:^(Picture-in-Picture)$
windowrule = size 960 540, title:^(Picture-in-Picture)$
windowrule = move 25%-, title:^(Picture-in-Picture)$
windowrule = float, title:^(imv|mpv|danmufloat|termfloat|nemo|ncmpcpp)$
windowrule = move 25%-, title:^(imv|mpv|danmufloat|termfloat|nemo|ncmpcpp)$
windowrule = size 960 540, title:^(imv|mpv|danmufloat|termfloat|nemo|ncmpcpp)$
windowrule = pin, title:^(danmufloat)$
windowrule = rounding 5, title:^(danmufloat|termfloat)$
windowrule = animation slide right, class:^(kitty|Alacritty)$
windowrule = noblur, class:^(org.mozilla.firefox)$
# Decorations related to floating windows on workspaces 1 to 10
windowrule = bordersize 2, floating:1, onworkspace:w[fv1-10]
windowrule = bordercolor $cachylblue, floating:1, onworkspace:w[fv1-10]
windowrule = rounding 8, floating:1, onworkspace:w[fv1-10]
# Decorations related to tiling windows on workspaces 1 to 10
windowrule = bordersize 3, floating:0, onworkspace:f[1-10]
windowrule = rounding 4, floating:0, onworkspace:f[1-10]
# Windows Rules End #

# Workspaces Rules https://wiki.hyprland.org/0.45.0/Configuring/Workspace-Rules/ #
# workspace = 1, default:true, monitor:$priMon
# workspace = 6, default:true, monitor:$secMon
# Workspace selectors https://wiki.hyprland.org/0.45.0/Configuring/Workspace-Rules/#workspace-selectors
# workspace = r[1-5], monitor:$priMon
# workspace = r[6-10], monitor:$secMon
# workspace = special:scratchpad, on-created-empty:$applauncher
# no_gaps_when_only deprecated instead workspaces rules with selectors can do the same
# Smart gaps from 0.45.0 https://wiki.hyprland.org/0.45.0/Configuring/Workspace-Rules/#smart-gaps
workspace = w[tv1-10], gapsout:5, gapsin:3
workspace = f[1], gapsout:5, gapsin:3
# Workspaces Rules End #

# Layers Rules #
layerrule = animation slide top, logout_dialog
# layerrule = animation popin 50%, waybar
layerrule = animation slide down, waybar
layerrule = animation fade 50%, wallpaper
# Layers Rules End #
